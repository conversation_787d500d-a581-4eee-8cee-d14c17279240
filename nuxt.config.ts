// https://nuxt.com/docs/api/configuration/nuxt-config


export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  devServer: {
    port: 3001, // 将端口号改为你需要的，例如 3001
    host: '0.0.0.0', // 如果需要允许外部访问，可以设置为 0.0.0.0
  },

  vite: {
    server: {
      hmr: {
        clientPort: 3001, // 替换成你当前服务的端口
      },
    },
    define: {
      'globalConfig.tenantId': JSON.stringify(process.env.TENANT_ID),
      'globalConfig.sk': JSON.stringify(process.env.SK),
      'globalConfig.payHide': process.env.PAY_HIDE === 'true',
      'globalConfig.payHideUrl': JSON.stringify(process.env.PAY_HIDE_URL),
      'globalConfig.payReturnUrl': JSON.stringify(process.env.PAY_RETURN_URL),
      'globalConfig.enableCaptcha':process.env.ENABLE_CAPTCHA === 'true',
      'globalConfig.turnstileKey':JSON.stringify(process.env.CAPTCHA_SITE_KEY),
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "@/assets/scss/_variables.scss" as *;'
        }
      }
    }
  },

  runtimeConfig: {
    public: {
      baseUrl: process.env.SERVER_BASE_URL || '',
      apiPath: process.env.SERVER_API_PATH || '',
      // tenantId: process.env.TENANT_ID || '0',
      shopName: process.env.SHOP_NAME || 'Shop',
      enableCaptcha:String(process.env.ENABLE_CAPTCHA).trim().toLowerCase() === 'true',
      turnstile: {
        siteKey: process.env.CAPTCHA_SITE_KEY,
        addValidateEndpoint: true
      },
      supportEmail: process.env.SUPPORT_EMAIL || '<EMAIL>',
      webUrl:process.env.WEB_URL|| 'http://localhost:3001',
      // payReturnUrl: process.env.PAY_RETURN_URL || 'http://localhost:3001/order/payment/return',
      // sk: process.env.SK || '',
    }
  },
  routeRules: {
    '/': { ssr: false },
    '/account/**': { ssr: false }, // 用户中心相关页面
    '/order/**': { ssr: false }, // 订单相关页面
    '/cart': { ssr: false },
    '/loading': { ssr: false },
    '/login': { ssr: false },
    '/help/**': { ssr: false },
    '/chat/**': { ssr: false },
  },
  app: {
    head: {
      script: [
        // <script src="./assets/styles/fonts/iconfont.js"></script>
        { src: '/fonts/iconfont.js', type: 'text/javascript' },
      ],
      link: [
        // <link rel="stylesheet" href="./assets/styles/fonts/iconfont.css">
        // { rel: 'stylesheet', href: "./assets/fonts/iconfont.css" },
      ],
    },
  },

  css: [
    '~/assets/scss/main.scss',
    '~/assets/scss/custom.scss',
    '~/assets/fonts/iconfont.css',
    'quasar/dist/quasar.prod.css'
  ],
  // vite: {
  //   css: {
  //     preprocessorOptions: {
  //       scss: {
  //         additionalData: '@use "@/assets/scss/_variables.scss" as *;'
  //       }
  //     }
  //   }
  // },
  modules: [
    '@pinia/nuxt',
    '@pinia-plugin-persistedstate/nuxt',
    '@nuxtjs/turnstile',
    'nuxt-quasar-ui',
    '@nuxt/eslint'
  ],
  quasar: {
    plugins: [
      'BottomSheet',
      'Dialog',
      'Loading',
      'LoadingBar',
      'Notify',
      'Dark',
    ],

    extras: {
      // font: 'roboto-font',
      fontIcons:[
        'material-icons',
        // 'material-icons-outlined',
        // 'material-icons-round',
        // 'material-icons-sharp',
        // 'material-symbols-outlined',
        // 'material-symbols-rounded',
        // 'material-symbols-sharp',
        // 'fontawesome-v5',
        // 'mdi-v6',
        'ionicons-v4', // 最后一个webfont在v4.6.3中可用。
        // 'eva-icons',
        'fontawesome-v6',
        // 'themify',
        // 'line-awesome',
        // 'bootstrap-icons'
      ]

    },
    components: {
      defaults: {
        QBtn: {
          // unelevated: true,
        },
      },
    },
  },
  plugins:[
    { src: './plugins/showNotify.client.js', mode: 'client' },
    { src: './plugins/globalConfig.js' },
  ]

})