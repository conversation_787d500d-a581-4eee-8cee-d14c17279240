<template>
  <!-- 公告栏和菜单 -->
  <Header />

  <div class="search-page">
    <div class="search-container">
      <!-- 搜索框 -->
      <div class="search-header">
        <h1 class="search-title">商品搜索</h1>
        <div class="search-bar">
          <div class="search-input-container">
            <input type="search" v-model="searchQuery" placeholder="搜索商品或链接" @keyup.enter="handleSearch" />
          </div>
          <div class="search-button-container" @click="handleSearch">
            <button class="search-button">
              <i class="iconfont icon-sousuo sousuo"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results">
        <!-- 搜索结果统计 -->
        <div class="search-stats" v-if="!searchStore.isEmptySearch">
          <p>
            <template v-if="searchStore.loading">正在搜索中...</template>
            <template v-else-if="searchStore.hasResults">
              找到 <span class="highlight">{{ searchStore.total }}</span> 个与 "<span class="highlight">{{ searchStore.searchQuery }}</span
              >" 相关的商品
            </template>
            <template v-else>
              没有找到与 "<span class="highlight">{{ searchStore.searchQuery }}</span
              >" 相关的商品
            </template>
          </p>
        </div>

        <!-- 加载中 -->
        <div v-if="searchStore.loading" class="loading-container">
          <q-spinner color="primary" size="3em" />
          <p>正在加载搜索结果...</p>
        </div>

        <!-- 空搜索提示 -->
        <div v-else-if="searchStore.isEmptySearch" class="empty-search">
          <i class="iconfont icon-sousuo empty-icon"></i>
          <p>请输入搜索关键词</p>
        </div>

        <!-- 无结果提示 -->
        <div v-else-if="!searchStore.hasResults" class="no-results">
          <i class="iconfont icon-zanwushuju empty-icon"></i>
          <p>没有找到相关商品</p>
          <p class="suggestion">您可以尝试使用其他关键词搜索</p>
        </div>

        <!-- 搜索结果列表 -->
        <div v-else class="product-grid">
          <div v-for="(product, index) in searchStore.searchResults" :key="product.id" class="product-item">
            <ProductBox :product="product" :index="index" />
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="searchStore.hasResults" class="pagination-container">
          <q-pagination v-model="currentPage" :max="searchStore.totalPages" :max-pages="6" boundary-numbers direction-links @update:model-value="handlePageChange" />
        </div>
      </div>
    </div>
  </div>

  <Footer />
  <!-- 侧边栏 -->
  <FixedBar />
</template>

<script setup>
import { useSearchStore } from '~/store/search';
import { useRoute, useRouter } from 'vue-router';

const route = useRoute();
const router = useRouter();
const searchStore = useSearchStore();

// 搜索查询参数
const searchQuery = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// 监听路由参数变化
watch(
  () => route.query,
  (newQuery) => {
    if (newQuery.q !== undefined) {
      searchQuery.value = decodeURIComponent(newQuery.q);
      searchStore.setSearchQuery(searchQuery.value);

      // 如果有页码参数，设置当前页码
      if (newQuery.page) {
        currentPage.value = parseInt(newQuery.page) || 1;
        searchStore.setCurrentPage(currentPage.value);
      } else {
        currentPage.value = 1;
        searchStore.setCurrentPage(1);
      }

      // 执行搜索
      searchStore.searchProducts(currentPage.value, pageSize.value);
    } else {
      // 清空搜索
      searchQuery.value = '';
      searchStore.setSearchQuery('');
      searchStore.clearSearchResults();
    }
  },
  { immediate: true }
);

// 处理搜索
const handleSearch = () => {
  if (searchQuery.value.trim()) {
    // 更新URL，保持在第一页
    router.push({
      path: '/search',
      query: {
        q: encodeURIComponent(searchQuery.value.trim()),
        page: 1,
      },
    });
  } else {
    // 如果搜索框为空，则清空搜索结果
    router.push('/search');
  }
};

// 处理分页变化
const handlePageChange = (page) => {
  if (searchQuery.value.trim()) {
    router.push({
      path: '/search',
      query: {
        q: encodeURIComponent(searchQuery.value.trim()),
        page: page,
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.search-page {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 16px;

  .search-container {
    width: 100%;

    .search-header {
      margin-bottom: 30px;
      text-align: center;

      .search-title {
        font-size: 28px;
        font-weight: bold;
        margin-bottom: 20px;
        color: #333;
      }

      .search-bar {
        position: relative;
        width: 100%;
        max-width: 600px;
        height: 46px;
        margin: 0 auto;
        display: flex;
        justify-content: center;

        .search-input-container {
          flex: 1;
          position: relative;
        }

        input[type='search'] {
          width: 100%;
          height: 46px;
          border-radius: 23px 0 0 23px;
          border: 2px solid #0073e6;
          border-right: none;
          padding: 0 20px;
          font-size: 16px;
          outline: none;
        }

        .search-button-container {
          width: 70px;
          height: 46px;
        }

        .search-button {
          width: 100%;
          height: 100%;
          background-color: #0073e6;
          border-radius: 0 23px 23px 0;
          border: none;
          cursor: pointer;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;

          .sousuo {
            font-size: 24px;
            color: #fff;
          }
        }
      }
    }

    .search-results {
      .search-stats {
        margin-bottom: 20px;
        font-size: 16px;
        color: #666;

        .highlight {
          color: #0073e6;
          font-weight: bold;
        }
      }

      .loading-container,
      .empty-search,
      .no-results {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 60px 0;
        color: #666;

        .empty-icon {
          font-size: 60px;
          margin-bottom: 20px;
          color: #ccc;
        }

        p {
          margin: 10px 0;
          font-size: 16px;
        }

        .suggestion {
          font-size: 14px;
          color: #999;
        }
      }

      .product-grid {
        display: grid;
        grid-template-columns: repeat(5, 1fr);
        gap: 16px;
        margin-bottom: 30px;

        @media (max-width: 1023px) and (min-width: 600px) {
          grid-template-columns: repeat(3, 1fr);
        }

        @media (max-width: 599px) {
          grid-template-columns: repeat(2, 1fr);
          gap: 10px;
        }

        .product-item {
          width: 100%;
        }
      }

      .pagination-container {
        display: flex;
        justify-content: center;
        margin: 30px 0;
      }
    }
  }
}

@media (max-width: 599px) {
  .search-page {
    padding: 15px 10px;

    .search-container {
      .search-header {
        margin-bottom: 20px;

        .search-title {
          font-size: 22px;
          margin-bottom: 15px;
        }

        .search-bar {
          height: 40px;

          input[type='search'] {
            height: 40px;
            font-size: 14px;
            padding: 0 15px;
          }

          .search-button-container {
            width: 60px;
            height: 40px;
          }

          .search-button {
            .sousuo {
              font-size: 20px;
            }
          }
        }
      }
    }
  }
}
</style>
