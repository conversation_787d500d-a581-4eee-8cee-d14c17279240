const AfterSaleApi = {
  // 获得售后分页
  getAfterSalePage: (params) => {
    return useClientGet(`/trade/after-sale/page`, {
      params,
    });
  },
  // 创建售后
  createAfterSale: (data) => {
    return useClientPost(`/trade/after-sale/create`, {
      data,
    });
  },
  // 获得售后
  getAfterSale: (id) => {
    return useClientGet(`/trade/after-sale/get`, {
      params: {
        id,
      },
    });
  },
  // 取消售后
  cancelAfterSale: (id) => {
    return useClientDelete(`/trade/after-sale/cancel`, {
      params: {
        id,
      },
    });
  },
  // 获得售后日志列表
  getAfterSaleLogList: (afterSaleId) => {
    return useClientGet(`/trade/after-sale-log/list`, {
      params: {
        afterSaleId,
      },
    });
  },
  // 退回货物
  deliveryAfterSale: (data) => {
    return useClientPut(`/trade/after-sale/delivery`, {
      data,
    });
  },
};

export default AfterSaleApi;
