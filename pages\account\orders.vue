<template>
  <div class="my-order">
    <!-- 标题区域 -->
    <div class="bg-grey-2 q-px-md q-py-sm">
      <div class="row items-center">
        <q-icon name="shopping_bag" size="xs" color="primary" class="q-mr-xs" />
        <span class="text-subtitle1 text-weight-medium">{{ $t('accountOrders.title') }}</span>
      </div>
    </div>

    <div class="q-pa-md">
      <div class="row justify-between items-center q-mb-md flex-wrap">
        <!-- 顶部 Tab -->
        <div class="col-12 col-sm-auto">
          <q-tabs
            v-model="state.currentTab"
            class="text-primary"
            active-color="primary"
            indicator-color="primary"
            align="left"
            narrow-indicator
            :dense="$q.screen.lt.sm"
            @update:model-value="onTabChange">
            <q-tab name="0" :label="$t('accountOrders.tabs.unpaid')" />
            <q-tab name="10" label="待采购" />
            <q-tab name="20" :label="$t('accountOrders.tabs.purchasing')" />
            <q-tab name="30" :label="$t('accountOrders.tabs.warehoused')" />
            <q-tab name="all" :label="$t('accountOrders.tabs.all')" />
          </q-tabs>
        </div>
        <div class="col-12 col-sm-auto q-mt-sm-none q-mt-md">
          <div class="search-container">
            <!-- 搜索框 -->
            <q-input v-model="state.searchNo" outlined dense clearable :placeholder="$t('accountOrders.search.placeholder')" @keyup.enter="getOrderList" class="search-input">
              <template #append>
                <q-btn flat dense icon="search" @click="getOrderList" />
              </template>
            </q-input>
          </div>
        </div>
      </div>

      <q-separator />

      <!-- 订单列表 -->
      <div>
        <!-- 表格标题行 - 仅在非移动端显示 -->
        <div v-if="!$q.screen.lt.sm" class="row items-center q-py-sm bg-grey-2 text-grey-8 q-mt-md rounded-borders-top">
          <div class="row col-9 items-center">
            <div class="col-6 text-center text-weight-medium">{{ $t('accountOrders.table.productDetails') }}</div>
            <div class="col-1 text-center text-weight-medium">{{ $t('accountOrders.table.unitPrice') }}</div>
            <div class="col-1 text-center text-weight-medium">{{ $t('accountOrders.table.quantity') }}</div>
            <div class="col-2 text-center text-weight-medium">{{ $t('accountOrders.table.amount') }}</div>
            <div class="col-2 text-center text-weight-medium">售后</div>
          </div>
          <div class="row col-3">
            <div class="col-5 text-center text-weight-medium">{{ $t('accountOrders.table.orderStatus') }}</div>
            <div class="col-7 text-center text-weight-medium">{{ $t('accountOrders.table.actions') }}</div>
          </div>
        </div>

        <!-- 有数据时显示订单列表 -->
        <div v-if="state.pagination.list.length > 0">
          <!-- 桌面视图 -->
          <div v-if="!$q.screen.lt.sm">
            <div v-for="order in state.pagination.list" :key="order.id" class="bg-white q-mb-md rounded-borders shadow-1">
              <div class="bg-grey-2 q-pa-sm q-px-md">
                <div class="row justify-between items-center">
                  <div>
                    <span class="text-weight-medium">{{ $t('accountOrders.orderInfo.orderNumber') }}</span>
                    <span class="text-primary">{{ order.no }}</span>
                    <span class="q-mx-md text-grey-7">{{ formatDateTime(order.createTime) }}</span>
                  </div>
                  <div>
                    <span class="text-weight-medium" v-if="order.hasSupplement && order.supplementStatus === 0">待补款：</span>
                    <span class="text-weight-medium" v-if="order.hasSupplement && order.supplementStatus === 1">已补款：</span>
                    <span class="text-red text-weight-bold order-amount" v-if="order.hasSupplement" v-html="formatAmount(order.supplementPrice)"></span>
                    &nbsp;
                    <span class="text-weight-medium">{{ $t('accountOrders.orderInfo.totalAmount') }}</span>
                    <span class="text-red text-weight-bold order-amount" v-html="formatAmount(order.payPrice + order.supplementPrice)"></span>

                    <q-btn flat round dense icon="delete" color="red" class="q-ml-sm" />
                    <q-btn flat round dense icon="chat" color="orange" class="q-ml-xs" />
                  </div>
                </div>
              </div>
              <div>
                <div class="row">
                  <div class="col-9">
                    <!-- 商品栏 -->
                    <div v-for="(item, index) in order.items" :key="item.id" class="row q-py-md q-px-sm" :class="{ 'border-top': index > 0 }">
                      <!-- 商品名称与图片 -->
                      <div class="col-6 row justify-start items-center">
                        <div>
                          <NuxtLink :to="`/product/${item.spuId}`">
                            <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 80px; height: 80px" class="rounded-borders q-mr-md" />
                          </NuxtLink>
                        </div>

                        <div class="column justify-around" style="flex: 1">
                          <div class="ellipsis-2-lines">
                            <NuxtLink :to="`/product/${item.spuId}`" class="text-weight-medium">
                              {{ item.spuName }}
                            </NuxtLink>
                          </div>
                          <div class="q-mt-sm text-grey-7 text-caption">{{ formattedProperties(item.properties) }}</div>
                        </div>
                      </div>
                      <!-- 单价 -->
                      <div class="col-1 row justify-center items-center">
                        <span class="price-amount" v-html="formatAmount(item.price)"></span>
                      </div>
                      <!-- 数量 -->
                      <div class="col-1 row justify-center items-center">{{ item.count }}</div>
                      <!-- 金额 -->
                      <div class="col-2 row justify-center items-center text-red text-weight-medium">
                        <span class="price-amount" v-html="formatAmount((item.price || item.sku?.price || 0) * item.count)"></span>
                      </div>
                      <!-- 售后 -->
                      <div class="col-2 row justify-center items-center">
                        <q-btn flat v-if="[10, 20, 30].includes(order.status) && item.afterSaleStatus === 0" color="primary" dense label="申请售后" @click="afterSale(item.id)" />
                        <div v-if="item.afterSaleStatus === 10">处理中</div>
                        <div v-if="item.afterSaleStatus === 20">处理完成</div>
                      </div>
                    </div>
                  </div>
                  <div class="row col-3">
                    <!-- 订单状态 -->
                    <div class="col-5 row justify-center items-center">
                      <q-badge :color="getStatusColor(order.status)" class="q-py-xs q-px-sm">
                        {{ getOrderStatus(order) }}
                      </q-badge>
                    </div>
                    <!-- 操作 -->
                    <div class="col-7 row justify-center items-center">
                      <div class="column items-center">
                        <q-btn flat color="primary" dense :label="$t('accountOrders.orderInfo.orderDetails')" class="q-mb-xs" @click="toOrderDetail(order.id)" />
                        <q-btn v-if="order.buttons.includes('cancel')" flat color="grey" dense :label="$t('accountOrders.orderInfo.cancelOrder')" class="q-mb-xs" @click="cancelOrder(order.id)" />
                        <q-btn
                          v-if="order.buttons.includes('pay')"
                          color="primary"
                          dense
                          icon-left="payment"
                          :label="$t('accountOrders.orderInfo.payNow')"
                          class="pay-btn"
                          @click="payOrder(order.payOrderId)" />
                        <q-btn v-if="order.buttons.includes('supplement')" color="primary" dense icon-left="payment" label="补款" class="pay-btn" @click="payOrderSupplement(order.supplementId)" />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 移动端卡片视图 -->
          <div v-else class="mobile-order-list">
            <q-card v-for="order in state.pagination.list" :key="order.id" class="order-card q-mb-md">
              <!-- 订单头部 -->
              <q-card-section class="order-header q-py-sm">
                <div class="row justify-between items-center">
                  <div class="order-no">
                    <span class="text-weight-medium">{{ $t('accountOrders.orderInfo.orderNumber') }}</span>
                    <span class="text-primary">{{ order.no }}</span>
                  </div>
                  <q-badge :color="getStatusColor(order.status)" class="status-badge">
                    {{ getOrderStatus(order.status) }}
                  </q-badge>
                </div>
                <div class="text-caption text-grey-7 q-mt-xs">{{ formatDateTime(order.createTime) }}</div>
              </q-card-section>

              <q-separator />

              <!-- 商品列表 -->
              <q-card-section class="q-pa-none">
                <div v-for="(item, index) in order.items" :key="item.id" class="product-item q-pa-md" :class="{ 'border-top': index > 0 }">
                  <div class="row no-wrap">
                    <!-- 商品图片 -->
                    <div class="product-img">
                      <NuxtLink :to="`/product/${item.spuId}`">
                        <q-img :src="getThumbnailUrl(item.picUrl, '80x80')" style="width: 70px; height: 70px" class="rounded-borders" />
                      </NuxtLink>
                    </div>

                    <!-- 商品信息 -->
                    <div class="product-info q-ml-md">
                      <div class="ellipsis-2-lines">
                        <NuxtLink :to="`/product/${item.spuId}`" class="text-weight-medium text-body2">
                          {{ item.spuName }}
                        </NuxtLink>
                      </div>
                      <div class="text-grey-7 text-caption q-mt-xs">{{ formattedProperties(item.properties) }}</div>
                      <div class="row justify-between items-center q-mt-xs">
                        <div class="text-red">
                          <span class="price-amount" v-html="formatAmount(item.price)"></span>
                        </div>
                        <div class="text-grey-7">x{{ item.count }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </q-card-section>

              <!-- 订单底部 -->
              <q-card-section class="order-footer q-py-sm bg-grey-1">
                <div class="row justify-between items-center">
                  <div class="order-total">
                    <span>{{ $t('accountOrders.orderInfo.totalItems', { count: order.items.length }) }}</span>
                    <span class="text-red text-weight-bold order-amount" v-html="formatAmount(order.payPrice)"></span>
                  </div>
                </div>

                <!-- 操作按钮 -->
                <div class="row justify-end q-mt-sm">
                  <q-btn flat dense size="sm" color="grey" icon="chat" class="q-mr-xs" />
                  <q-btn flat dense size="sm" color="grey" icon="delete" class="q-mr-sm" />
                  <q-btn outline size="sm" color="primary" :label="$t('accountOrders.orderInfo.orderDetails')" class="q-mr-sm" @click="toOrderDetail(order.id)" />
                  <q-btn v-if="order.buttons.includes('cancel')" outline size="sm" color="grey" :label="$t('accountOrders.orderInfo.cancelOrder')" class="q-mr-sm" @click="cancelOrder(order.id)" />
                  <q-btn
                    v-if="order.buttons.includes('pay')"
                    color="primary"
                    size="sm"
                    icon-left="payment"
                    :label="$t('accountOrders.orderInfo.payNow')"
                    class="pay-btn"
                    @click="payOrder(order.payOrderId)" />
                </div>
              </q-card-section>
            </q-card>
          </div>
        </div>

        <!-- 无数据 -->
        <div v-else class="column items-center q-py-lg">
          <q-icon name="shopping_bag" size="3em" color="grey-5" />
          <div class="text-grey-7 q-mt-sm">{{ $t('accountOrders.noOrders') }}</div>
        </div>

        <!-- 分页控件和记录信息 -->
        <div class="row justify-between items-center q-mt-md flex-wrap" v-if="state.pagination.total > 0">
          <div class="col-12 col-sm-auto q-mb-sm q-mb-sm-none">
            <div class="row justify-end justify-sm-start">
              <div class="text-caption text-grey-8">
                {{
                  $t('accountOrders.pagination', {
                    total: state.pagination.total,
                    current: state.pagination.pageNo,
                    totalPages: Math.ceil(state.pagination.total / state.pagination.pageSize) || 1,
                  })
                }}
              </div>
            </div>
          </div>
          <div class="col-12 col-sm-auto">
            <div class="row justify-end">
              <q-pagination
                v-model="state.pagination.pageNo"
                :max="Math.ceil(state.pagination.total / state.pagination.pageSize)"
                :max-pages="$q.screen.lt.sm ? 3 : 6"
                boundary-links
                direction-links
                :dense="$q.screen.lt.sm"
                @update:model-value="onPageChange" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import OrderApi from '~/composables/orderApi';
import { useQuasar } from 'quasar';
import { useI18n } from 'vue-i18n';
import { useCurrency } from '~/composables/useCurrency';
import { PayOrderSources } from '../../utils/constants';
import SupplementApi from '../../composables/supplementApi';

definePageMeta({
  middleware: 'auth', // 引入身份验证中间件
});

const $q = useQuasar();
const { t } = useI18n();
const { formatAmount } = useCurrency();

// 数据
const state = reactive({
  currentTab: '0', // 选中的 tabMaps 下标
  pagination: {
    list: [],
    total: 0,
    pageNo: 1,
    pageSize: 5,
  },
  loadStatus: '',
  searchNo: null,
});

onMounted(() => {
  getOrderList();
});

// 获取订单列表
async function getOrderList() {
  state.loadStatus = 'loading';
  if (state.searchNo) {
    state.currentTab = 'all';
  }
  const currentStatus = state.currentTab !== 'all' ? parseInt(state.currentTab, 10) : null;
  const { code, data } = await OrderApi.getOrderPage({
    pageNo: state.pagination.pageNo,
    pageSize: state.pagination.pageSize,
    status: currentStatus,
    commentStatus: state.currentTab === '30' ? false : null,
    no: state.searchNo,
  });
  if (code !== 0) {
    return;
  }

  data.list.forEach((order) => handleOrderButtons(order));
  state.pagination.list = data.list;
  state.pagination.total = data.total;
  state.loadStatus = state.pagination.list.length < state.pagination.total ? 'more' : 'noMore';
}

function onTabChange() {
  state.pagination.pageNo = 1;
  getOrderList();
}

// 处理分页切换
function onPageChange(page) {
  state.pagination.pageNo = page; // 更新当前页码
  getOrderList(); // 刷新当前页数据
}

// 跳转到订单详情页面
function toOrderDetail(orderId) {
  if (!orderId) {
    showNotify(t('accountOrders.errors.emptyOrderId'), 'negative');
    return;
  }

  // 使用路由导航到订单详情页面，并传递订单ID作为查询参数
  navigateTo({
    path: '/account/order-detail',
    query: { id: orderId },
  });
}

// 取消订单
function cancelOrder(orderId) {
  if (!orderId) {
    showNotify(t('accountOrders.errors.emptyOrderId'), 'negative');
    return;
  }

  // 显示确认对话框
  $q.dialog({
    title: t('accountOrders.confirmations.cancelTitle'),
    message: t('accountOrders.confirmations.cancelMessage'),
    cancel: true,
    persistent: true,
  }).onOk(() => {
    // 调用取消订单API
    OrderApi.cancelOrder(orderId)
      .then(({ code }) => {
        if (code === 0) {
          showNotify(t('accountOrders.confirmations.cancelSuccess'), 'positive');
          getOrderList(); // 刷新订单列表
        } else {
          showNotify(t('accountOrders.errors.cancelFailed'), 'negative');
        }
      })
      .catch((error) => {
        console.error(t('accountOrders.errors.cancelError') + ':', error);
        showNotify(t('accountOrders.errors.cancelError'), 'negative');
      });
  });
}

// 支付订单
function payOrder(pId) {
  if (!pId) {
    showNotify(t('accountOrders.errors.emptyOrderId'), 'negative');
    return;
  }

  // 跳转到支付页面
  navigateTo({
    path: '/pay',
    query: { pId, ps: PayOrderSources.ORDER },
  });
}
// 支付订单补款单
async function payOrderSupplement(supplementId) {
  const { code, data } = await SupplementApi.getDetail(supplementId);
  if (code === 0) {
    if (data.payOrderId) {
      // 跳转到支付页面
      navigateTo({
        path: '/pay',
        query: { pId: data.payOrderId, ps: PayOrderSources.ORDER },
      });
    }
  }
}

function getOrderStatus(order) {
  switch (order.status) {
    case 0:
      return t('accountOrders.status.unpaid');
    case 10:
      if (order.hasSupplement && order.supplementStatus === 0) {
        return '待补款';
      } else {
        return '待采购';
      }

    case 20:
      return t('accountOrders.status.purchasing');
    case 30:
      return t('accountOrders.status.warehoused');
    case 40:
      return t('accountOrders.status.cancelled');
    default:
      return t('accountOrders.status.unknown');
  }
}

// 获取订单状态对应的颜色
function getStatusColor(status) {
  switch (status) {
    case 0:
      return 'orange';
    case 10:
      return 'blue';
    case 20:
      return 'teal';
    case 30:
      return 'teal';
    case 40:
      return 'grey';
    case 50:
      return 'deep-orange';
    case 100:
      return 'positive';
    case -1:
      return 'grey';
    default:
      return 'grey';
  }
}

// 注意：原 fen2yuan 函数已被 formatAmount 替代，用于支持多币别显示

function handleOrderButtons(order) {
  order.buttons = [];
  if (order.status === 20) {
    // 确认收货
    order.buttons.push('confirm');
  }
  if (order.logisticsId > 0) {
    // 查看物流
    order.buttons.push('express');
  }
  if (order.status === 0) {
    // 取消订单 / 发起支付
    order.buttons.push('cancel');
    order.buttons.push('pay');
  }
  if (order.status === 10 && order.hasSupplement && order.supplementStatus == 0) {
    // 补款单
    order.buttons.push('supplement');
  }
  if (order.status === 30 && !order.commentStatus) {
    // 发起评价
    order.buttons.push('comment');
  }
  if (order.status === 40) {
    // 删除订单
    order.buttons.push('delete');
  }
}
</script>

<style lang="scss" scoped>
.my-order {
  .q-tab {
    font-weight: 500;
    padding: 0 16px;
    min-height: 36px;

    @media (max-width: 599px) {
      padding: 0 12px;
      min-height: 32px;
      font-size: 14px;
    }
  }

  .q-tabs {
    min-height: 36px;

    @media (max-width: 599px) {
      min-height: 32px;
    }
  }

  .q-pagination {
    .q-btn {
      font-weight: 500;
      padding: 0 8px;
      min-height: 32px;

      @media (max-width: 599px) {
        padding: 0 6px;
        min-height: 28px;
      }
    }
  }

  .border-top {
    border-top: 1px solid #e0e0e0;
  }

  .rounded-borders-top {
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
  }

  .ellipsis-2-lines {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
    min-height: 2em;
    line-height: 1.2;
  }

  .q-badge {
    font-size: 0.8rem;
  }

  // 移动端卡片样式
  .mobile-order-list {
    .order-card {
      border-radius: 8px;
      overflow: hidden;

      .order-header {
        background-color: #f5f5f5;
      }

      .status-badge {
        font-size: 12px;
        padding: 4px 8px;
      }

      .product-img {
        width: 70px;
        flex-shrink: 0;
      }

      .product-info {
        flex: 1;
        min-width: 0;
      }

      .order-total {
        font-size: 14px;
      }
    }
  }

  // 搜索框和按钮容器
  .search-container {
    display: flex;
    align-items: center;
    width: 100%;

    @media (max-width: 599px) {
      justify-content: space-between;
    }
  }

  // 搜索框样式
  .search-input {
    width: 100%;
    max-width: 280px;
    flex: 1;

    @media (max-width: 599px) {
      max-width: none;
      flex: 1;
    }
  }

  // 支付按钮样式
  .pay-btn {
    border-radius: 4px;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.12);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 3px 5px rgba(0, 0, 0, 0.2);
      transform: translateY(-1px);
    }
  }

  // 响应式辅助类
  .q-mt-sm-none {
    @media (min-width: 600px) {
      margin-top: 0;
    }
  }

  .justify-sm-start {
    @media (min-width: 600px) {
      justify-content: flex-start;
    }
  }

  .q-mb-sm-none {
    @media (min-width: 600px) {
      margin-bottom: 0;
    }
  }

  // 金额显示样式
  .order-amount,
  .price-amount {
    :deep(.primary-currency) {
      white-space: nowrap;
      display: inline-block;
      font-weight: 500;

      :deep(.default-currency) {
        font-size: 0.85em;
        color: #666;
        opacity: 0.85;
        font-weight: normal;
        display: inline-block;
        margin-left: 4px;
        white-space: normal;
      }
    }
  }

  // 订单金额样式
  .order-amount {
    :deep(.primary-currency) {
      font-size: 1.1rem;
    }
  }

  // 商品价格样式
  .price-amount {
    :deep(.primary-currency) {
      font-size: 0.95rem;
    }
  }
}
</style>
