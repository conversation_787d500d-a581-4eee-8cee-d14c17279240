import { defineStore } from 'pinia';
import ProductApi from '~/composables/productApi';

export const useSearchStore = defineStore({
  id: 'search',
  state: () => ({
    searchResults: [], // 搜索结果列表
    searchQuery: '', // 搜索关键词
    loading: false, // 加载状态
    total: 0, // 总记录数
    pageSize: 10, // 每页大小
    currentPage: 1, // 当前页码
  }),
  actions: {
    // 设置搜索关键词
    setSearchQuery(query) {
      this.searchQuery = query;
    },
    
    // 设置当前页码
    setCurrentPage(page) {
      this.currentPage = page;
    },
    
    // 设置每页大小
    setPageSize(size) {
      this.pageSize = size;
    },
    
    // 执行搜索
    async searchProducts(page = this.currentPage, pageSize = this.pageSize) {
      this.loading = true;
      
      try {
        const response = await ProductApi.searchProducts({
          pageNo: page,
          pageSize: pageSize,
          name: this.searchQuery, // 搜索关键词
        });
        
        if (response.code === 0) {
          this.searchResults = response.data.list || [];
          this.total = response.data.total || 0;
          this.currentPage = page;
          this.pageSize = pageSize;
        } else {
          console.error('搜索失败:', response.msg);
          this.searchResults = [];
          this.total = 0;
        }
      } catch (error) {
        console.error('搜索出错:', error);
        this.searchResults = [];
        this.total = 0;
      } finally {
        this.loading = false;
      }
      
      return {
        results: this.searchResults,
        total: this.total,
      };
    },
    
    // 清空搜索结果
    clearSearchResults() {
      this.searchResults = [];
      this.total = 0;
    },
  },
  getters: {
    // 获取总页数
    totalPages: (state) => {
      return Math.ceil(state.total / state.pageSize);
    },
    
    // 判断是否有搜索结果
    hasResults: (state) => {
      return state.searchResults.length > 0;
    },
    
    // 判断是否为空搜索
    isEmptySearch: (state) => {
      return !state.searchQuery || state.searchQuery.trim() === '';
    },
  },
});
